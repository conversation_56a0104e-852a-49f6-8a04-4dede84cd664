<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dialog Force Close Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Dialog Force Close Test</h1>
        <p>This page tests the force close dialog functionality.</p>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test Controls</h3>
                <button class="btn btn-primary mb-2" onclick="showTestModal()">Show Test Modal</button><br>
                <button class="btn btn-warning mb-2" onclick="utils.showLoading(true, 'Testing loading...')">Show Loading Modal</button><br>
                <button class="btn btn-success mb-2" onclick="utils.showLoading(false)">Hide Loading Modal</button><br>
                <button class="btn btn-danger mb-2" onclick="utils.forceCloseDialog('testModal')">Force Close Test Modal</button><br>
                <button class="btn btn-danger mb-2" onclick="utils.forceCloseDialog('loadingModal')">Force Close Loading Modal</button><br>
                <button class="btn btn-dark mb-2" onclick="utils.forceCloseAllDialogs()">Force Close All Dialogs</button><br>
            </div>
            <div class="col-md-6">
                <h3>Keyboard Shortcuts</h3>
                <p><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>Escape</kbd> - Force close all dialogs</p>
                
                <h3>Instructions</h3>
                <ol>
                    <li>Click "Show Test Modal" to open a modal</li>
                    <li>Try closing it normally with the X button</li>
                    <li>Open it again and try the force close button</li>
                    <li>Test the loading modal similarly</li>
                    <li>Try the keyboard shortcut</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Test Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a test modal to verify force close functionality.</p>
                    <p>You can close this modal normally or use the force close buttons.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Save changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">Loading...</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script>
        function showTestModal() {
            const modal = new bootstrap.Modal(document.getElementById('testModal'));
            modal.show();
        }
    </script>
</body>
</html>
